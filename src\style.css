:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 优化颜色方案 */
  --primary-color: #3B82F6;
  --primary-light: #60A5FA;
  --primary-dark: #2563EB;
  --secondary-color: #10B981;
  --accent-color: #F59E0B;
  --text-primary: #1F2937;
  --text-secondary: #4B5563;
  --text-light: #9CA3AF;
  --bg-light: #F9FAFB;
  --bg-white: #FFFFFF;
  --bg-card: #FFFFFF;
  --border-color: #E5E7EB;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius: 8px;
  
  color-scheme: light dark;
  color: var(--text-primary);
  background-color: var(--bg-light);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: inherit;
  transition: color 0.2s ease;
}
a:hover {
  color: var(--primary-dark);
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--bg-light);
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

h1 {
  font-size: 2.25rem;
}

h2 {
  font-size: 1.875rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
}

button {
  border-radius: var(--radius);
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--bg-light);
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  border-color: var(--primary-color);
  background-color: var(--bg-white);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

.card {
  background-color: var(--bg-card);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

#app {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

/* 修改 Element Plus 主题颜色 */
:root {
  --el-color-primary: var(--primary-color);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  h1 {
    font-size: 1.875rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #F9FAFB;
    --text-secondary: #E5E7EB;
    --text-light: #9CA3AF;
    --bg-light: #111827;
    --bg-white: #1F2937;
    --bg-card: #1F2937;
    --border-color: #374151;
  }
  
  button {
    background-color: #2D3748;
  }
  
  button:hover {
    background-color: #374151;
  }
}
