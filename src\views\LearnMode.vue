<template>
  <div class="learn-container">
    <div v-if="loading" class="loading-container">
      <el-empty description="正在准备学习内容...">
        <el-skeleton :rows="3" animated />
      </el-empty>
    </div>
    
    <div v-else-if="!currentWord" class="no-words-container">
      <el-result
        icon="warning"
        title="暂无学习内容"
        sub-title="当前没有可学习的单词，请添加新单词或稍后再试"
      >
        <template #extra>
          <el-button type="primary" @click="goToAdd" class="action-button" round>
            <el-icon><Plus /></el-icon> 添加单词
          </el-button>
          <el-button @click="goBack" class="action-button" round>返回</el-button>
        </template>
      </el-result>
    </div>
    
    <div v-else class="word-learning-container">
      <div class="learning-header">
        <div class="learning-mode">
          <el-tag 
            :type="isReviewMode ? 'warning' : 'primary'"
            effect="dark"
            size="large"
            round
          >
            {{ isReviewMode ? '复习模式' : '学习模式' }}
          </el-tag>
        </div>
        <div class="progress-info">
          <el-progress 
            type="circle" 
            :percentage="Math.round((currentIndex + 1) / learningWords.length * 100)" 
            :width="50"
          />
          <span class="progress-text">{{ currentIndex + 1 }} / {{ learningWords.length }}</span>
        </div>
      </div>
      
      <transition name="flip" mode="out-in">
        <el-card 
          :key="showingAnswer ? 'answer' : 'question'" 
          class="word-card"
          :body-style="{ padding: '30px 20px' }"
        >
          <div v-if="showingAnswer" class="word-answer animate__animated animate__fadeIn">
            <h2 class="word-spelling">{{ currentWord.spelling }}</h2>
            <el-button 
              type="primary"
              circle
              @click="playPronunciation(currentWord.spelling)" 
              class="pronunciation-button"
            >
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <div class="word-definition-container">
              <p class="word-definition">{{ currentWord.definition }}</p>
            </div>
            <div v-if="currentWord.example" class="word-example">
              <span class="example-label">例句:</span>
              <p>{{ currentWord.example }}</p>
            </div>
            
            <div class="feedback-buttons">
              <p class="feedback-title">这个单词的难度如何？</p>
              <div class="difficulty-buttons">
                <el-button type="danger" round @click="recordDifficulty('hard')">困难</el-button>
                <el-button type="warning" round @click="recordDifficulty('medium')">一般</el-button>
                <el-button type="success" round @click="recordDifficulty('easy')">简单</el-button>
              </div>
            </div>
          </div>
          
          <div v-else class="word-question animate__animated animate__fadeIn">
            <template v-if="isReviewMode">
              <!-- 复习模式 - 先显示释义 -->
              <div class="definition-container">
                <h3 class="question-label">请根据释义回想单词：</h3>
                <p class="word-definition">{{ currentWord.definition }}</p>
                <div v-if="currentWord.example" class="word-example">
                  <span class="example-label">例句:</span>
                  <p v-html="processExampleSentence(currentWord.example, currentWord.spelling)"></p>
                </div>
              </div>
            </template>
            <template v-else>
              <!-- 学习模式 - 先显示单词 -->
              <h2 class="word-spelling">{{ currentWord.spelling }}</h2>
              <el-button 
                type="primary"
                circle
                @click="playPronunciation(currentWord.spelling)" 
                class="pronunciation-button"
              >
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              <p class="hint-text">请尝试记住这个单词</p>
            </template>
            
            <div class="spelling-practice" v-if="isReviewMode">
              <el-input
                v-model="userInput"
                placeholder="请输入单词拼写"
                @keydown.enter="checkSpelling"
                class="spelling-input"
                :status="spellingCorrect === false ? 'error' : (spellingCorrect === true ? 'success' : '')"
              ></el-input>
              <el-button type="primary" @click="checkSpelling" round>检查</el-button>
            </div>
          </div>
        </el-card>
      </transition>
      
      <div class="action-buttons">
        <el-button 
          v-if="currentIndex > 0 && showingAnswer" 
          @click="prevWord" 
          type="info" 
          plain 
          round
          class="prev-button"
        >
          上一个
        </el-button>
        <el-button v-if="!showingAnswer" @click="showAnswer" type="primary" plain round>
          {{ isReviewMode ? '显示单词' : '显示释义' }}
        </el-button>
        <el-button 
          v-else 
          type="primary" 
          @click="nextWord" 
          round
          class="next-button"
          :icon="isLastWord ? 'Check' : 'Right'"
        >
          {{ isLastWord ? '上一个' : '下一个' }}
        </el-button>
        <el-button @click="goBack" round>退出学习</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { VideoPlay, Plus, Right, Check,ArrowLeft  } from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()
const loading = ref(true)
const learningWords = ref([])
const currentIndex = ref(0)
const showingAnswer = ref(false)
const userInput = ref('')
const spellingCorrect = ref(null)

// 检查是否处于复习模式
const isReviewMode = computed(() => 
  store.state.settings.learningMode === 'review'
)

// 获取当前单词
const currentWord = computed(() => {
  if (learningWords.value.length === 0 || currentIndex.value >= learningWords.value.length) {
    return null
  }
  return learningWords.value[currentIndex.value]
})

// 检查是否是最后一个单词
const isLastWord = computed(() => {
  return currentIndex.value === learningWords.value.length - 1
})

// 组件挂载时加载单词
onMounted(() => {
  prepareWords()
  loading.value = false
})

// 准备学习单词
const prepareWords = () => {
  const allWords = store.state.words
  if (allWords.length === 0) {
    return
  }
  
  // 从设置中获取每日单词数量
  const dailyCount = store.state.settings.dailyWordCount
  
  // 在这个演示中，我们简单地取前N个单词
  // 在实际应用中，你可能会实现间隔重复算法
  learningWords.value = allWords.slice(0, dailyCount)
}

// 处理例句，将单词替换为下划线
const processExampleSentence = (sentence, word) => {
  if (!sentence || !word) return sentence;
  
  // 创建区分大小写的正则表达式
  const regex = new RegExp(word, 'gi');
  
  // 如果用户拼写正确，或者已经显示答案，则显示完整例句
  if (spellingCorrect.value === true || showingAnswer.value) {
    return sentence;
  }
  
  // 用下划线替换单词
  const underlineLength = word.length;
  const underline = `<span class="word-underline">${'_'.repeat(underlineLength)}</span>`;
  
  return sentence.replace(regex, underline);
}

// 显示答案
const showAnswer = () => {
  showingAnswer.value = true
}

// 复习模式下检查拼写
const checkSpelling = () => {
  if (!userInput.value) {
    ElMessage.warning('请输入单词拼写')
    return
  }
  
  spellingCorrect.value = userInput.value.toLowerCase() === currentWord.value.spelling.toLowerCase()
  
  if (spellingCorrect.value) {
    ElMessage.success('拼写正确！')
    
    // 自动在1.5秒后显示答案
    setTimeout(() => {
      showingAnswer.value = true
    }, 1500)
  } else {
    ElMessage.error('拼写错误，请重试或查看答案')
  }
}

// 播放发音
const playPronunciation = (word) => {
  const utterance = new SpeechSynthesisUtterance(word)
  utterance.lang = 'en-US'
  window.speechSynthesis.speak(utterance)
}

// 记录当前单词的难度级别
const recordDifficulty = (level) => {
  // 这里根据用户反馈更新单词的学习状态
  // 在实际应用中，应该存储这些数据用于间隔重复算法
  ElMessage.success(`已记录您对该单词的难度评价: ${level}`)
  nextWord()
}
// 添加上一个单词的方法
const prevWord = () => {
  if (currentIndex.value > 0) {
    // 回退到上一个单词
    showingAnswer.value = false
    userInput.value = ''
    spellingCorrect.value = null
    currentIndex.value--
  }
}
// 进入下一个单词
const nextWord = () => {
  // 更新学习进度
  if (currentIndex.value === learningWords.value.length - 1) {
    // 最后一个单词完成
    const progress = {
      dailyCompleted: store.state.learningProgress.dailyCompleted + learningWords.value.length
    }
    store.commit('updateProgress', progress)
    ElMessage.success({
      message: '恭喜，今日学习任务完成！',
      type: 'success',
      duration: 2000,
      onClose: () => { router.push('/') }
    })
  } else {
    // 进入下一个单词
    showingAnswer.value = false
    userInput.value = ''
    spellingCorrect.value = null
    currentIndex.value++
  }
}

// 前往添加单词页面
const goToAdd = () => {
  router.push('/add')
}

// 返回首页
const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.learn-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 160px);
}

.loading-container,
.no-words-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.word-learning-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.learning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.learning-mode {
  font-weight: 600;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.word-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  border: none;
  background: linear-gradient(145deg, var(--bg-white), var(--bg-light));
  overflow: hidden;
}

.word-spelling {
  font-size: 2.2rem;
  text-align: center;
  margin: 0 0 15px;
  color: var(--primary-color);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pronunciation-button {
  display: block;
  margin: 0 auto 20px;
  font-size: 1.2rem;
  box-shadow: var(--shadow);
}

.word-definition-container {
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  border-left: 4px solid var(--primary-color);
}

.word-definition {
  font-size: 1.2rem;
  text-align: center;
  margin: 0;
  line-height: 1.6;
}

.word-example {
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  border-left: 4px solid var(--accent-color);
}

.example-label {
  font-weight: bold;
  color: var(--accent-color);
  margin-right: 5px;
}

.word-underline {
  border-bottom: 2px solid var(--primary-color);
  padding: 0 2px;
  font-weight: 600;
  color: var(--text-light);
  letter-spacing: 1px;
}

.feedback-buttons {
  margin-top: 30px;
  text-align: center;
}

.feedback-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-secondary);
}

.difficulty-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.spelling-practice {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}

.spelling-input {
  border-radius: 20px;
}

.definition-container {
  text-align: center;
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.question-label {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-secondary);
}

.hint-text {
  text-align: center;
  color: var(--text-light);
  font-style: italic;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.action-button {
  margin-top: 15px;
}

.next-button {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 翻转动画 */
.flip-enter-active,
.flip-leave-active {
  transition: transform 0.5s, opacity 0.5s;
  transform-style: preserve-3d;
}

.flip-enter-from,
.flip-leave-to {
  opacity: 0;
  transform: rotateY(90deg);
}

/* 添加响应式设计 */
@media (max-width: 768px) {
  .learn-container {
    padding: 10px;
  }
  
  .word-spelling {
    font-size: 1.8rem;
  }
  
  .word-definition {
    font-size: 1rem;
  }
  
  .difficulty-buttons {
    flex-wrap: wrap;
  }
}
</style> 