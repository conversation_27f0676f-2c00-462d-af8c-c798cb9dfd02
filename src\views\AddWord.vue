<template>
  <div class="add-word-container">
    <h2 class="page-title">{{ isEditing ? '编辑单词' : '添加单词' }}</h2>
    
    <el-form 
      :model="wordForm" 
      :rules="rules" 
      ref="wordFormRef" 
      label-width="100px"
      class="word-form"
    >
      <el-form-item label="单词" prop="spelling">
        <el-input v-model="wordForm.spelling" placeholder="请输入单词" />
      </el-form-item>
      
      <el-form-item label="发音" class="pronunciation-item">
        <el-button @click="playPronunciation" :disabled="!wordForm.spelling">
          <el-icon><VideoPlay /></el-icon>
          播放发音
        </el-button>
      </el-form-item>
      
      <el-form-item label="释义" prop="definition">
        <el-input 
          v-model="wordForm.definition" 
          type="textarea" 
          rows="3" 
          placeholder="请输入单词释义"
        />
      </el-form-item>
      
      <el-form-item label="例句">
        <el-input 
          v-model="wordForm.example" 
          type="textarea" 
          rows="3" 
          placeholder="请输入例句（可选）"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()
const route = useRoute()
const wordFormRef = ref(null)

const isEditing = computed(() => route.params.id !== undefined)

const wordForm = reactive({
  id: '',
  spelling: '',
  definition: '',
  example: ''
})

const rules = {
  spelling: [
    { required: true, message: '请输入单词', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  definition: [
    { required: true, message: '请输入释义', trigger: 'blur' }
  ]
}

onMounted(() => {
  if (isEditing.value) {
    const id = route.params.id
    const word = store.state.words.find(w => w.id === id)
    
    if (word) {
      wordForm.id = word.id
      wordForm.spelling = word.spelling
      wordForm.definition = word.definition
      wordForm.example = word.example || ''
    } else {
      ElMessage.error('未找到要编辑的单词')
      router.push('/')
    }
  }
})

const playPronunciation = () => {
  if (wordForm.spelling) {
    const utterance = new SpeechSynthesisUtterance(wordForm.spelling)
    utterance.lang = 'en-US'
    window.speechSynthesis.speak(utterance)
  }
}

const submitForm = () => {
  wordFormRef.value.validate((valid) => {
    if (valid) {
      if (isEditing.value) {
        store.commit('updateWord', { ...wordForm })
        ElMessage.success('单词更新成功')
      } else {
        const newWord = { 
          ...wordForm,
          id: Date.now().toString(), 
          createdAt: new Date()
        }
        store.commit('addWord', newWord)
        ElMessage.success('单词添加成功')
      }
      router.push('/')
    }
  })
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.add-word-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px 0;
}

.page-title {
  margin-bottom: 30px;
  text-align: center;
  color: #409EFF;
}

.word-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pronunciation-item {
  margin-bottom: 22px;
}
</style> 