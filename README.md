# 个性化单词本应用

一个帮助用户高效学习和记忆单词的 Vue3 应用程序，具有简洁易用的界面和丰富的功能。

## 功能特点

- **单词管理**：添加、编辑、删除单词，包括单词拼写、释义和例句
- **发音功能**：使用浏览器内置文本转语音功能提供单词发音
- **学习模式**：提供"学习模式"和"复习模式"，适应不同学习阶段的需求
- **拼写练习**：在复习模式中进行单词拼写练习，加深记忆
- **学习统计**：通过图表直观展示学习进度和学习效率
- **个性化设置**：自定义每日学习单词数量、复习频率等
- **数据导入导出**：支持将词库导出为JSON格式，也可以导入已有词库

## 技术栈

- Vue 3 (使用 Composition API 和 setup 语法糖)
- Vuex 4 (状态管理)
- Vue Router 4 (路由管理)
- Element Plus (UI组件库)
- ECharts (数据可视化)
- LocalStorage (本地数据存储)

## 应用结构

- **主界面**：展示单词列表，支持搜索，单词操作和学习入口
- **添加/编辑界面**：添加或修改单词及相关信息
- **学习模式界面**：进行单词学习和复习
- **设置界面**：调整学习计划和应用设置
- **统计界面**：展示学习进度和效果统计

## 运行项目

```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 构建生产版本
npm run build
```

## 数据持久化

应用使用浏览器的 LocalStorage 进行数据持久化存储，包括：
- 单词库数据
- 用户设置
- 学习进度记录

## 功能展望

- 支持单词图片关联，增强记忆
- 添加标签系统，对单词进行分类
- 实现间隔重复算法，优化复习计划
- 添加用户账户系统，实现云端数据同步
- 开发移动端应用，支持随时随地学习
