<script setup>
import { Plus, Setting, HomeFilled, VideoCameraFilled } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const activeRoute = computed(() => route.path)

// 是否显示底部导航
const showBottomNav = ref(true)
</script>

<template>
  <div class="app-container">
    <el-container>
      <el-header height="70px">
        <div class="header-content">
          <router-link to="/" class="logo-link">
            <h1 class="app-title">个性化单词本</h1>
          </router-link>
          <div class="header-buttons">
            <router-link to="/add">
              <el-button type="primary" circle class="action-button">
                <el-icon><Plus /></el-icon>
              </el-button>
            </router-link>
            <router-link to="/settings">
              <el-button circle class="action-button">
                <el-icon><Setting /></el-icon>
              </el-button>
            </router-link>
          </div>
        </div>
      </el-header>
      <el-main>
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
      
      <!-- 底部导航栏 -->
      <el-footer v-if="showBottomNav" height="60px" class="bottom-nav">
        <router-link to="/" class="nav-item" :class="{ active: activeRoute === '/' }">
          <el-icon><HomeFilled /></el-icon>
          <span>首页</span>
        </router-link>
        <router-link to="/learn" class="nav-item" :class="{ active: activeRoute === '/learn' }">
          <el-icon><VideoCameraFilled /></el-icon>
          <span>学习</span>
        </router-link>
        <router-link to="/settings" class="nav-item" :class="{ active: activeRoute === '/settings' }">
          <el-icon><Setting /></el-icon>
          <span>设置</span>
        </router-link>
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped>
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 1rem;
  border-bottom: 1px solid var(--border-color);
}

.logo-link {
  text-decoration: none;
}

.app-title {
  margin: 0;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.75rem;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.action-button {
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.el-main {
  padding: 1.5rem;
  flex: 1;
  background-color: var(--bg-light);
}

/* 底部导航栏 */
.bottom-nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: var(--bg-white);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-light);
  padding: 0.5rem;
  text-decoration: none;
  transition: color 0.2s ease;
  width: 33.33%;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-item .el-icon {
  font-size: 1.5rem;
  margin-bottom: 4px;
}

.nav-item span {
  font-size: 0.8rem;
}

/* 淡入淡出过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 媒体查询，适配小屏幕 */
@media (max-width: 768px) {
  .app-title {
    font-size: 1.5rem;
  }
  
  .el-main {
    padding: 1rem;
  }
}
</style>
