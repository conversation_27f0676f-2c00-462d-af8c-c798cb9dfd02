<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2 class="page-title">学习设置</h2>
      <p class="page-subtitle">自定义您的学习体验</p>
    </div>
    
    <el-card class="settings-card">
      <el-tabs type="border-card" class="settings-tabs">
        <el-tab-pane label="学习设置">
          <el-form 
            :model="settingsForm" 
            label-width="120px"
            class="settings-form"
          >
            <el-form-item label="每日学习单词数">
              <el-slider 
                v-model="settingsForm.dailyWordCount" 
                :min="1" 
                :max="50"
                :step="1"
                :marks="{1: '1', 10: '10', 20: '20', 30: '30', 40: '40', 50: '50'}"
                show-input
                class="settings-slider"
              />
              <p class="setting-description">
                设置每天需要学习的单词数量，建议设置合理数量保持学习动力
              </p>
            </el-form-item>
            
            <el-form-item label="复习频率">
              <el-select 
                v-model="settingsForm.reviewFrequency" 
                placeholder="请选择复习频率"
                class="settings-select"
              >
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>
              <p class="setting-description">
                设置复习单词的频率，复习是巩固记忆的关键
              </p>
            </el-form-item>
            
            <el-form-item label="学习模式">
              <el-radio-group v-model="settingsForm.learningMode" class="mode-selection">
                <el-radio-button label="learn">
                  <div class="mode-option">
                    <div class="mode-icon">
                      <el-icon><DocumentAdd /></el-icon>
                    </div>
                    <div class="mode-text">
                      <span class="mode-title">学习模式</span>
                      <span class="mode-description">单词 → 释义</span>
                    </div>
                  </div>
                </el-radio-button>
                <el-radio-button label="review">
                  <div class="mode-option">
                    <div class="mode-icon">
                      <el-icon><Reading /></el-icon>
                    </div>
                    <div class="mode-text">
                      <span class="mode-title">复习模式</span>
                      <span class="mode-description">释义 → 单词</span>
                    </div>
                  </div>
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveSettings" round class="save-button">
                <el-icon><Check /></el-icon> 保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="数据管理">
          <div class="data-management">
            <div class="export-section">
              <h3 class="section-title">
                <el-icon><Download /></el-icon> 导出词库
              </h3>
              <p class="help-text">将你的词库导出为JSON文件备份</p>
              <el-button @click="exportWords" type="primary" plain round class="export-button">
                导出为JSON
              </el-button>
            </div>
            
            <el-divider class="custom-divider" />
            
            <div class="import-section">
              <h3 class="section-title">
                <el-icon><Upload /></el-icon> 导入词库
              </h3>
              <p class="help-text">导入之前备份的词库或其他词库文件，支持多次导入不同词库，已存在的单词将被自动忽略</p>
              <el-upload
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :limit="10"
                multiple
                class="import-upload"
              >
                <el-button type="info" plain round>选择JSON文件</el-button>
              </el-upload>
              <el-button 
                type="primary" 
                @click="importWords" 
                :disabled="!selectedFiles.length"
                round
                class="import-button"
              >
                导入
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <div class="actions-footer">
      <el-button @click="goBack" round class="back-button" icon="Back">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Check, 
  Download, 
  Upload, 
  Back, 
  DocumentAdd, 
  Reading 
} from '@element-plus/icons-vue'

const store = useStore()
const router = useRouter()
const selectedFiles = ref([])

const settingsForm = reactive({
  dailyWordCount: 10,
  reviewFrequency: 'daily',
  learningMode: 'learn'
})

onMounted(() => {
  const currentSettings = store.state.settings
  settingsForm.dailyWordCount = currentSettings.dailyWordCount
  settingsForm.reviewFrequency = currentSettings.reviewFrequency
  settingsForm.learningMode = currentSettings.learningMode
})

// 保存设置
const saveSettings = () => {
  store.commit('updateSettings', { ...settingsForm })
  ElMessage({
    message: '设置已保存',
    type: 'success',
    icon: Check
  })
  router.push('/')
}

// 处理导入文件的选择
const handleFileChange = (file, fileList) => {
  selectedFiles.value = fileList.map(f => f.raw)
}

// 从JSON文件导入单词
const importWords = async () => {
  if (!selectedFiles.value.length) {
    ElMessage.warning('请先选择JSON文件')
    return
  }
  
  let totalImported = 0
  let totalIgnored = 0
  let totalFiles = 0
  
  try {
    // 获取当前词库
    const currentWords = store.state.words
    // 创建一个Set来存储已有单词的拼写，用于快速查找
    let existingSpellings = new Set(currentWords.map(word => word.spelling))
    
    // 存储所有要导入的新单词
    const allNewWords = []
    
    for (const file of selectedFiles.value) {
      const fileContent = await readFileAsync(file)
      try {
        const data = JSON.parse(fileContent)
        if (Array.isArray(data)) {
          totalFiles++
          
          // 过滤出尚未存在的单词
          const newWords = data.filter(word => !existingSpellings.has(word.spelling))
          
          // 更新统计数据
          totalImported += newWords.length
          totalIgnored += data.length - newWords.length
          
          // 如果有新单词，则收集起来
          if (newWords.length > 0) {
            allNewWords.push(...newWords)
            
            // 更新存在的拼写集合，防止后续文件中的重复单词被导入
            newWords.forEach(word => existingSpellings.add(word.spelling))
          }
        }
      } catch (error) {
        console.error('解析文件失败：', error)
        ElMessage.error(`解析文件 ${file.name} 失败：${error.message}`)
      }
    }
    
    // 更新词库
    if (allNewWords.length > 0) {
      // 使用addWords mutation添加所有新单词
      store.commit('addWords', allNewWords)
      
      ElMessage.success(`已处理 ${totalFiles} 个文件，成功导入 ${totalImported} 个新单词，已忽略 ${totalIgnored} 个已存在的单词`)
    } else {
      ElMessage.info('没有导入新单词，所有单词都已存在')
    }
    
    // 清空文件选择
    selectedFiles.value = []
    
  } catch (error) {
    ElMessage.error('导入失败：' + error.message)
  }
}

// 将文件读取包装为Promise
const readFileAsync = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file)
  })
}

// 导出单词到JSON文件
const exportWords = () => {
  const words = store.state.words
  if (words.length === 0) {
    ElMessage.warning('词库为空，没有可导出的单词')
    return
  }
  
  const dataStr = JSON.stringify(words, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
  
  const exportName = 'mywords_' + new Date().toISOString().split('T')[0] + '.json'
  
  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', exportName)
  linkElement.click()
  
  ElMessage.success('词库导出成功')
}

// 返回首页
const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
}

.settings-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  margin-bottom: 8px;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--text-light);
  margin: 0;
  font-size: 1rem;
}

.settings-card {
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: none;
  overflow: hidden;
}

.settings-tabs {
  border: none;
}

.settings-tabs :deep(.el-tabs__header) {
  background: linear-gradient(90deg, var(--bg-light), var(--bg-white));
  margin: 0;
  border-radius: 12px 12px 0 0;
}

.settings-tabs :deep(.el-tabs__item) {
  font-weight: 600;
  padding: 16px 24px;
}

.settings-tabs :deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
}

.settings-form {
  padding: 20px;
}

.settings-slider {
  width: 100%;
  margin-top: 10px;
}

.settings-select {
  width: 100%;
  max-width: 300px;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--text-light);
  margin: 20px 0px 0px 0px;
  line-height: 1.5;
}

.mode-selection {
  width: 100%;
  display: flex;
  gap: 15px;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.mode-option:hover {
  background-color: var(--bg-light);
}

.mode-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 10px;
  color: white;
}

.mode-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.mode-title {
  font-weight: 600;
  font-size: 1rem;
}

.mode-description {
  font-size: 0.75rem;
  color: var(--text-light);
  margin-top: 4px;
}

.save-button {
  padding: 12px 28px;
  font-weight: 600;
  /* background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); */
  border: none;
  /* box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); */
}

.data-management {
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-primary);
  margin-bottom: 10px;
  font-size: 1.2rem;
  font-weight: 600;
}

.help-text {
  color: var(--text-light);
  margin: 8px 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.import-upload {
  margin-bottom: 15px;
}

.import-button,
.export-button {
  padding: 10px 24px;
  font-weight: 500;
}

.export-section, 
.import-section {
  padding: 20px 0;
}

.custom-divider {
  margin: 30px 0;
}

.actions-footer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.back-button {
  padding: 12px 28px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 1.8rem;
  }
  
  .mode-selection {
    flex-direction: column;
  }
  
  .settings-form {
    padding: 15px;
  }
  
  .settings-tabs :deep(.el-tabs__item) {
    padding: 12px 16px;
  }
}
</style>