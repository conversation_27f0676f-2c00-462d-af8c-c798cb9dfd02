import { createApp } from 'vue'
import { createStore } from 'vuex'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import App from './App.vue'

import Home from './views/Home.vue'
import AddWord from './views/AddWord.vue'
import LearnMode from './views/LearnMode.vue'
import Settings from './views/Settings.vue'


const loadState = () => {
  try {
    const words = localStorage.getItem('myword_words')
    const settings = localStorage.getItem('myword_settings')
    const learningProgress = localStorage.getItem('myword_progress')

    return {
      words: words ? JSON.parse(words) : [],
      settings: settings ? JSON.parse(settings) : {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      },
      learningProgress: learningProgress ? JSON.parse(learningProgress) : {
        dailyCompleted: 0,
        history: []
      }
    }
  } catch (e) {
    console.error('Error loading state from localStorage:', e)
    return {
      words: [],
      settings: {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      },
      learningProgress: {
        dailyCompleted: 0,
        history: []
      }
    }
  }
}

const initialState = loadState()

const store = createStore({
  state() {
    return initialState
  },
  mutations: {
    addWord(state, word) {
      state.words.push(word);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    addWords(state, wordsArray) {
      state.words.push(...wordsArray);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    deleteWord(state, wordId) {
      state.words = state.words.filter(word => word.id !== wordId);
      localStorage.setItem('myword_words', JSON.stringify(state.words))
    },
    updateWord(state, updatedWord) {
      const index = state.words.findIndex(word => word.id === updatedWord.id);
      if (index !== -1) {
        state.words[index] = updatedWord;
        localStorage.setItem('myword_words', JSON.stringify(state.words))
      }
    },
    updateSettings(state, settings) {
      state.settings = { ...state.settings, ...settings };
      localStorage.setItem('myword_settings', JSON.stringify(state.settings))
    },
    updateProgress(state, progress) {
      state.learningProgress = { ...state.learningProgress, ...progress };
      const today = new Date().toISOString().split('T')[0];
      const historyIndex = state.learningProgress.history.findIndex(item => item.date === today);
      if (historyIndex !== -1) {
        state.learningProgress.history[historyIndex].count = progress.dailyCompleted;
      } else {
        state.learningProgress.history.push({
          date: today,
          count: progress.dailyCompleted
        });
      }
      localStorage.setItem('myword_progress', JSON.stringify(state.learningProgress))
    },
    resetState(state) {
      state.words = [];
      state.settings = {
        dailyWordCount: 10,
        reviewFrequency: 'daily',
        learningMode: 'learn'
      };
      state.learningProgress = {
        dailyCompleted: 0,
        history: []
      };
      localStorage.removeItem('myword_words');
      localStorage.removeItem('myword_settings');
      localStorage.removeItem('myword_progress');
    }
  }
})

const routes = [
  { path: '/', component: Home },
  { path: '/add', component: AddWord },
  { path: '/edit/:id', component: AddWord },
  { path: '/learn', component: LearnMode },
  { path: '/settings', component: Settings },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(store)
app.use(router)
app.use(ElementPlus)
app.mount('#app')
